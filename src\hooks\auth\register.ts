import { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { authApi, RegisterData } from '../../lib/api';
import { useNavigate } from 'react-router-dom';

interface UseRegisterReturn {
  register: (userData: RegisterData) => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

export function useRegister(): UseRegisterReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { login: authLogin } = useAuth();
  const navigate = useNavigate();

  const register = async (userData: RegisterData) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await authApi.register(userData);
      
      if (response.success) {
        // Store token and user data in context
        authLogin(response.data.token, response.data.user);
        
        // Redirect to dashboard
        navigate('/dashboard');
      } else {
        setError(response.message || 'Registration failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return {
    register,
    isLoading,
    error,
  };
}
