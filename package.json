{"name": "cheff-up", "private": true, "version": "0.0.7", "type": "module", "scripts": {"prebuild": "bun run check-version.ts", "dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@tailwindcss/vite": "^4.1.13", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "express-rate-limit": "^8.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.544.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.9.2", "sqlite3": "^5.1.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.13"}, "devDependencies": {"@eslint/js": "^9.36.0", "@types/bcryptjs": "^3.0.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.5.2", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@types/sqlite3": "^5.1.0", "@vitejs/plugin-react": "^5.0.3", "eslint": "^9.36.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "shadcn": "^3.3.1", "tw-animate-css": "^1.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.44.0", "vite": "^7.1.7"}}