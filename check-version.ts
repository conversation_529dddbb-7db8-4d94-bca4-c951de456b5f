import fs from "fs";
import packageJson from "./package.json";

const currentVersion = packageJson.version;

// Validate version format
if (!/^\d+\.\d+\.\d+$/.test(currentVersion)) {
    throw new Error(`Invalid version format: ${currentVersion}. Expected format: x.y.z`);
}

const [majorStr, minorStr, patchStr] = currentVersion.split(".");
const major = parseInt(majorStr, 10);
const minor = parseInt(minorStr, 10);
const patch = parseInt(patchStr, 10);

// Validate parsed numbers
if (isNaN(major) || isNaN(minor) || isNaN(patch)) {
    throw new Error(`Failed to parse version numbers from: ${currentVersion}`);
}

let newVersion = "";

if (patch === 9) {
    const newPatch = patch + 1;
    let newMinor = minor;
    let newMajor = major;

    newVersion = `${newMajor}.${newMinor}.${newPatch}`;
    newMinor = 0;
    newMajor = major + 1;
} else {
    const newPatch = patch + 1;
    const newMinor = minor;
    const newMajor = major;

    newVersion = `${newMajor}.${newMinor}.${newPatch}`;
}

if (newVersion !== currentVersion) {
    packageJson.version = newVersion;
    fs.writeFileSync("./package.json", JSON.stringify(packageJson, null, 2));
    console.log(`Version updated to ${newVersion}`);
} else {
    console.log("No version update required");
}